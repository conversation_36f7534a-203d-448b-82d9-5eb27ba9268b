// 创建悬浮窗元素
function createFloatingWindow() {
    // 检查是否已经存在悬浮窗
    if (document.getElementById('boss-floating-window')) {
        return;
    }

    // 创建悬浮窗容器
    const floatingWindow = document.createElement('div');
    floatingWindow.id = 'boss-floating-window';
    floatingWindow.innerHTML = `
        <div class="boss-floating-header">
            <span class="boss-floating-title">码哥自动化脚本</span>
            <button class="boss-floating-close">×</button>
        </div>
        <div class="boss-floating-content">
            <p>这是浏览器页面上的悬浮窗！</p>
            <button class="boss-floating-action">执行操作</button>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        #boss-floating-window {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            width: 280px;
            height: 200px;
            background: linear-gradient(135deg, rgba(58, 22, 177, 0.4), rgba(121, 203, 14, 0.3));
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 999999;
            opacity: 0.9;
            cursor: move;
            user-select: none;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            transition: opacity 0.3s ease, box-shadow 0.3s ease;
        }

        #boss-floating-window:hover {
            opacity: 1;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        #boss-floating-window.dragging {
            transition: none;
            cursor: grabbing;
        }

        .boss-floating-header {
            padding: 15px 20px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .boss-floating-title {
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .boss-floating-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .boss-floating-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .boss-floating-content {
            padding: 15px 20px 20px;
            text-align: center;
        }

        .boss-floating-content p {
            margin-bottom: 15px;
            font-size: 13px;
            line-height: 1.4;
            opacity: 0.9;
        }

        .boss-floating-action {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .boss-floating-action:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }
    `;

    // 添加样式到页面
    document.head.appendChild(style);
    document.body.appendChild(floatingWindow);

    // 添加拖动功能
    addDragFunctionality(floatingWindow);

    // 添加事件监听
    addEventListeners(floatingWindow);
}

// 添加拖动功能
function addDragFunctionality(element) {
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    element.addEventListener('mousedown', function(e) {
        // 如果点击的是按钮，不启动拖动
        if (e.target.classList.contains('boss-floating-close') || 
            e.target.classList.contains('boss-floating-action')) {
            return;
        }

        isDragging = true;
        element.classList.add('dragging');

        const rect = element.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        const x = e.clientX - dragOffset.x;
        const y = e.clientY - dragOffset.y;

        // 限制拖动范围
        const maxX = window.innerWidth - element.offsetWidth;
        const maxY = window.innerHeight - element.offsetHeight;

        const constrainedX = Math.max(0, Math.min(x, maxX));
        const constrainedY = Math.max(0, Math.min(y, maxY));

        element.style.left = constrainedX + 'px';
        element.style.top = constrainedY + 'px';
        element.style.right = 'auto';
        element.style.transform = 'none';
    });

    document.addEventListener('mouseup', function() {
        if (isDragging) {
            isDragging = false;
            element.classList.remove('dragging');
        }
    });

    // 双击重置位置
    element.addEventListener('dblclick', function(e) {
        if (!e.target.classList.contains('boss-floating-close') && 
            !e.target.classList.contains('boss-floating-action')) {
            resetPosition(element);
        }
    });
}

// 重置位置
function resetPosition(element) {
    element.style.right = '20px';
    element.style.top = '50%';
    element.style.left = 'auto';
    element.style.transform = 'translateY(-50%)';
}

// 添加事件监听
function addEventListeners(element) {
    const closeBtn = element.querySelector('.boss-floating-close');
    const actionBtn = element.querySelector('.boss-floating-action');

    closeBtn.addEventListener('click', function() {
        element.remove();
    });

    actionBtn.addEventListener('click', function() {
        alert('在浏览器页面上执行了操作！');
    });
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'showFloatingWindow') {
        createFloatingWindow();
        sendResponse({success: true});
    }
});
