<html>
    <head>
        <title>popup</title>
        <meta charset="utf-8">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                width: 300px;
                height: 300px;
                background: linear-gradient(135deg, rgba(58, 22, 177, 0.8), rgba(121, 203, 14, 0.6));
                border-radius: 20px;
                padding: 20px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                overflow: hidden;
                position: relative;
            }

            /* 添加内部圆角容器 */
            .container {
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 15px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            h1 {
                color: #ffffff;
                font-size: 18px;
                text-align: center;
                margin-bottom: 20px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                line-height: 1.4;
            }

            button {
                background: linear-gradient(45deg, #79cb0e, #3a16b1);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }

            button:active {
                transform: translateY(0);
            }
        </style>
    </head>
    <!--圆角特效浏览器插件-->
    <body>
        <div class="container">
            <h1 id="message">码哥自动化脚本定制</h1>
            <button id="btn">测试按钮</button>
        </div>
    </body>
</html>

