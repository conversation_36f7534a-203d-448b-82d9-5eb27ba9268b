<html>
    <head>
        <title>popup</title>
        <meta charset="utf-8">
        <style>
            * {
                margin: 0; 
                padding: 0;
                box-sizing: border-box;
            }

            body {
                width: 300px;
                height: 300px;
                background: linear-gradient(135deg, rgba(208, 22, 208, 0.8), rgba(42, 14, 203, 0.6));
                border-radius: 20px;
                padding: 20px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                overflow: hidden;
                position: relative;
            }

            /* 添加内部圆角容器 */
            .container {
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 15px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            h1 {
                color: #ffffff;
                font-size: 18px;
                text-align: center;
                margin-bottom: 20px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                line-height: 1.4;
            }

            button {
                background: linear-gradient(45deg, #79cb0e, #3a16b1);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }

            button:active {
                transform: translateY(0);
            }

            /* 悬浮窗样式 */
            .floating-window {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0);
                width: 250px;
                height: 200px;
                background: linear-gradient(135deg, rgba(58, 22, 177, 0.95), rgba(121, 203, 14, 0.85));
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                z-index: 1000;
                transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
                opacity: 0;
                visibility: hidden;
            }

            .floating-window.show {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
                visibility: visible;
            }

            .floating-window h2 {
                color: white;
                font-size: 16px;
                margin-bottom: 15px;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .floating-window p {
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                line-height: 1.5;
                text-align: center;
                margin-bottom: 20px;
            }

            .floating-window .close-btn {
                position: absolute;
                top: 10px;
                right: 15px;
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 25px;
                height: 25px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;
            }

            .floating-window .close-btn:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: none;
                box-shadow: none;
            }

            .floating-window .action-btn {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                margin: 0 auto;
                display: block;
            }

            .floating-window .action-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            }
        </style>
    </head>
    <!--圆角特效浏览器插件-->
    <body>
        <div class="container">
            <h1 id="message">码哥自动化脚本定制</h1>
            <button id="btn">测试按钮</button>
        </div>

        <!-- 悬浮窗 -->
        <div id="floatingWindow" class="floating-window">
            <button class="close-btn" id="closeBtn">×</button>
            <h2>悬浮窗标题</h2>
            <p>这是一个漂亮的悬浮窗，你可以在这里添加任何内容！</p>
            <button class="action-btn" id="actionBtn">执行操作</button>
        </div>

        <script src="popup.js"></script>
    </body>
</html>

