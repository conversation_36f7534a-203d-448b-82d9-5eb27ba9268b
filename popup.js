document.addEventListener('DOMContentLoaded', function() {
    const btn = document.getElementById('btn');
    const floatingWindow = document.getElementById('floatingWindow');
    const closeBtn = document.getElementById('closeBtn');
    const actionBtn = document.getElementById('actionBtn');
    
    // 点击测试按钮显示悬浮窗
    btn.addEventListener('click', function() {
        floatingWindow.classList.add('show');
    });
    
    // 点击关闭按钮隐藏悬浮窗
    closeBtn.addEventListener('click', function() {
        floatingWindow.classList.remove('show');
    });
    
    // 点击悬浮窗外部区域关闭悬浮窗
    floatingWindow.addEventListener('click', function(e) {
        if (e.target === floatingWindow) {
            floatingWindow.classList.remove('show');
        }
    });
    
    // 悬浮窗内的操作按钮
    actionBtn.addEventListener('click', function() {
        alert('执行了操作！');
        // 这里可以添加你的具体功能
    });
    
    // ESC键关闭悬浮窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && floatingWindow.classList.contains('show')) {
            floatingWindow.classList.remove('show');
        }
    });
});
