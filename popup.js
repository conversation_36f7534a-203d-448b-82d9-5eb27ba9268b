document.addEventListener('DOMContentLoaded', function() {
    const btn = document.getElementById('btn');
    const floatingWindow = document.getElementById('floatingWindow');
    const closeBtn = document.getElementById('closeBtn');
    const actionBtn = document.getElementById('actionBtn');

    // 拖动相关变量
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    // 点击测试按钮显示悬浮窗
    btn.addEventListener('click', function() {
        // 在当前标签页显示悬浮窗
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const tabId = tabs[0].id;

            // 先尝试发送消息
            chrome.tabs.sendMessage(tabId, {action: 'showFloatingWindow'}, function(response) {
                if (chrome.runtime.lastError) {
                    // 如果content script未加载，先注入再发送消息
                    console.log('Content script未加载，正在注入...');
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        files: ['content.js']
                    }, function() {
                        if (chrome.runtime.lastError) {
                            console.error('无法注入content script:', chrome.runtime.lastError.message);
                            alert('无法在此页面显示悬浮窗，请尝试刷新页面或在其他网页上使用。');
                            return;
                        }

                        // 注入成功后再次发送消息
                        setTimeout(() => {
                            chrome.tabs.sendMessage(tabId, {action: 'showFloatingWindow'}, function(response) {
                                if (chrome.runtime.lastError) {
                                    console.error('发送消息失败:', chrome.runtime.lastError.message);
                                } else {
                                    console.log('悬浮窗已在页面上显示');
                                }
                            });
                        }, 100);
                    });
                } else {
                    console.log('悬浮窗已在页面上显示');
                }
            });
        });

        // 同时在popup中也显示（可选）
        floatingWindow.classList.add('show');
    });

    // 点击关闭按钮隐藏悬浮窗
    closeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        floatingWindow.classList.remove('show');
        // 重置位置到右边居中
        resetPosition();
    });

    // 悬浮窗内的操作按钮
    actionBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        alert('执行了操作！');
        // 这里可以添加你的具体功能
    });

    // ESC键关闭悬浮窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && floatingWindow.classList.contains('show')) {
            floatingWindow.classList.remove('show');
            resetPosition();
        }
    });

    // 重置悬浮窗位置到右边居中
    function resetPosition() {
        floatingWindow.style.right = '20px';
        floatingWindow.style.top = '50%';
        floatingWindow.style.left = 'auto';
        floatingWindow.style.transform = 'translateY(-50%)';
    }

    // 拖动功能
    floatingWindow.addEventListener('mousedown', function(e) {
        // 如果点击的是关闭按钮或操作按钮，不启动拖动
        if (e.target === closeBtn || e.target === actionBtn) {
            return;
        }

        isDragging = true;
        floatingWindow.classList.add('dragging');

        const rect = floatingWindow.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        const x = e.clientX - dragOffset.x;
        const y = e.clientY - dragOffset.y;

        // 限制拖动范围，防止拖出窗口
        const maxX = window.innerWidth - floatingWindow.offsetWidth;
        const maxY = window.innerHeight - floatingWindow.offsetHeight;

        const constrainedX = Math.max(0, Math.min(x, maxX));
        const constrainedY = Math.max(0, Math.min(y, maxY));

        floatingWindow.style.left = constrainedX + 'px';
        floatingWindow.style.top = constrainedY + 'px';
        floatingWindow.style.right = 'auto';
        floatingWindow.style.transform = 'none';
    });

    document.addEventListener('mouseup', function() {
        if (isDragging) {
            isDragging = false;
            floatingWindow.classList.remove('dragging');
        }
    });

    // 双击重置位置
    floatingWindow.addEventListener('dblclick', function(e) {
        if (e.target !== closeBtn && e.target !== actionBtn) {
            resetPosition();
        }
    });
});
