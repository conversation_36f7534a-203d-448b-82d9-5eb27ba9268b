<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss插件测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .description {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .instructions {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .step {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .note {
            background: rgba(255,193,7,0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            border: 1px solid rgba(255,193,7,0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        
        .feature h3 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Boss插件测试页面</h1>
        
        <div class="description">
            欢迎使用Boss自动化脚本插件！<br>
            这是一个专门用于测试插件功能的页面。
        </div>
        
        <div class="instructions">
            <h2>📋 使用说明</h2>
            <div class="step">
                <strong>步骤 1:</strong> 确保Boss插件已安装并启用
            </div>
            <div class="step">
                <strong>步骤 2:</strong> 点击浏览器工具栏中的Boss插件图标
            </div>
            <div class="step">
                <strong>步骤 3:</strong> 在弹出的窗口中点击"测试按钮"
            </div>
            <div class="step">
                <strong>步骤 4:</strong> 悬浮窗将出现在页面右侧，可以拖动和操作
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🎨 半透明效果</h3>
                <p>悬浮窗具有现代化的半透明毛玻璃效果</p>
            </div>
            <div class="feature">
                <h3>🖱️ 拖动功能</h3>
                <p>可以拖动悬浮窗到页面任意位置</p>
            </div>
            <div class="feature">
                <h3>📍 智能定位</h3>
                <p>启动时自动定位到右边居中位置</p>
            </div>
            <div class="feature">
                <h3>🔄 位置重置</h3>
                <p>双击悬浮窗可重置到初始位置</p>
            </div>
        </div>
        
        <div class="note">
            <strong>💡 提示：</strong> 如果悬浮窗没有出现，请检查：
            <ul style="text-align: left; margin-top: 10px;">
                <li>插件是否正确安装</li>
                <li>是否在支持的页面上（普通网页）</li>
                <li>尝试刷新页面后重试</li>
                <li>检查浏览器控制台是否有错误信息</li>
            </ul>
        </div>
    </div>
</body>
</html>
